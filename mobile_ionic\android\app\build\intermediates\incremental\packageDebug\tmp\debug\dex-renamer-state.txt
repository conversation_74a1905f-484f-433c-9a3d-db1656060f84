#Thu Jun 26 01:22:46 PST 2025
base.0=C\:\\Users\\junre\\junrelCAPSTONE\\Capstone\\mobile_ionic\\android\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
base.1=C\:\\Users\\junre\\junrelCAPSTONE\\Capstone\\mobile_ionic\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\12\\classes.dex
base.10=C\:\\Users\\junre\\junrelCAPSTONE\\Capstone\\mobile_ionic\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\9\\classes.dex
base.11=C\:\\Users\\junre\\junrelCAPSTONE\\Capstone\\mobile_ionic\\android\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.12=C\:\\Users\\junre\\junrelCAPSTONE\\Capstone\\mobile_ionic\\android\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\7\\classes.dex
base.13=C\:\\Users\\junre\\junrelCAPSTONE\\Capstone\\mobile_ionic\\android\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
base.2=C\:\\Users\\junre\\junrelCAPSTONE\\Capstone\\mobile_ionic\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\13\\classes.dex
base.3=C\:\\Users\\junre\\junrelCAPSTONE\\Capstone\\mobile_ionic\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\14\\classes.dex
base.4=C\:\\Users\\junre\\junrelCAPSTONE\\Capstone\\mobile_ionic\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\2\\classes.dex
base.5=C\:\\Users\\junre\\junrelCAPSTONE\\Capstone\\mobile_ionic\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\3\\classes.dex
base.6=C\:\\Users\\junre\\junrelCAPSTONE\\Capstone\\mobile_ionic\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\4\\classes.dex
base.7=C\:\\Users\\junre\\junrelCAPSTONE\\Capstone\\mobile_ionic\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\5\\classes.dex
base.8=C\:\\Users\\junre\\junrelCAPSTONE\\Capstone\\mobile_ionic\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\7\\classes.dex
base.9=C\:\\Users\\junre\\junrelCAPSTONE\\Capstone\\mobile_ionic\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\8\\classes.dex
path.0=classes.dex
path.1=12/classes.dex
path.10=9/classes.dex
path.11=0/classes.dex
path.12=7/classes.dex
path.13=classes2.dex
path.2=13/classes.dex
path.3=14/classes.dex
path.4=2/classes.dex
path.5=3/classes.dex
path.6=4/classes.dex
path.7=5/classes.dex
path.8=7/classes.dex
path.9=8/classes.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.10=classes11.dex
renamed.11=classes12.dex
renamed.12=classes13.dex
renamed.13=classes14.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
renamed.6=classes7.dex
renamed.7=classes8.dex
renamed.8=classes9.dex
renamed.9=classes10.dex
