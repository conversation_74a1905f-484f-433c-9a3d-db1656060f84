import{a as mt}from"./chunk-GEGX2RE6.js";import{a as ut,c as _t}from"./chunk-YZWAW4SM.js";import{a as ht}from"./chunk-QZ5PEPJK.js";import{b as dt}from"./chunk-I4SN7ED3.js";import{a as gt}from"./chunk-LSM7X32V.js";import"./chunk-3J7GGTVR.js";import{a as pt}from"./chunk-WPPT3EJF.js";import"./chunk-2LL5MXLB.js";import{$ as E,B as s,Eb as X,F as M,G as m,I as b,Ib as tt,J as i,Jb as et,K as a,L as d,Mb as nt,O as F,P as _,Q as h,Sb as ot,Tb as it,X as c,Y as T,Z as O,Zb as at,_ as N,aa as D,ba as R,bc as rt,ca as $,cb as G,d as A,dc as lt,fb as j,fc as st,gc as ct,ib as K,l as x,m as I,ma as z,na as V,pa as U,r as y,ra as W,s as k,sb as Y,wa as B,wb as q,xb as H,ya as J,yb as Q,zb as Z}from"./chunk-SFXIJNIZ.js";import"./chunk-LR6AIEJQ.js";import"./chunk-6NVMNNPA.js";import"./chunk-KW2BML7M.js";import"./chunk-SV2ZKNWA.js";import"./chunk-YJDO75HI.js";import"./chunk-HC6MZPB3.js";import"./chunk-Q5Q6EGIP.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-ZJ5IMUT4.js";import"./chunk-SGSBBWFA.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-EI2QJP5N.js";import"./chunk-W6U2AR23.js";import"./chunk-APL3YEA6.js";import"./chunk-HSXX7Y3C.js";import"./chunk-FUGLTCJS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import{a as S,b as L,f as ft,g as f}from"./chunk-2R6CW7ES.js";var p=ft(_t());function Ct(l,C){if(l&1&&(i(0,"div",45)(1,"ion-card")(2,"ion-card-content")(3,"div",46),d(4,"ion-icon",47),i(5,"span"),c(6,"Route to Nearest Center"),a()(),i(7,"div",39)(8,"div",48),d(9,"ion-icon",38),i(10,"span"),c(11),a()(),i(12,"div",48),d(13,"ion-icon",49),i(14,"span"),c(15),a()()()()()()),l&2){let t=h();s(9),m("name",t.travelMode==="walking"?"walk-outline":t.travelMode==="cycling"?"bicycle-outline":"car-outline"),s(2),O("",(t.routeTime/60).toFixed(0)," min"),s(4),O("",(t.routeDistance/1e3).toFixed(2)," km")}}function vt(l,C){if(l&1&&(i(0,"span",58),c(1),a()),l&2){let t=h().$implicit,n=h();s(),O(" \u{1F4CD} ",n.calculateDistanceInKm(t)," km away ")}}function Mt(l,C){if(l&1){let t=F();i(0,"div",50),_("click",function(){let e=y(t).$implicit,o=h();return k(o.selectCenterFromList(e))}),i(1,"div",51)(2,"h4"),c(3),a(),i(4,"p",52),c(5),a(),i(6,"div",53),M(7,vt,2,1,"span",54),i(8,"span",55),c(9),a()()(),i(10,"div",56),d(11,"ion-icon",57),a()()}if(l&2){let t=C.$implicit,n=h();s(3),T(t.name),s(2),T(t.address),s(2),m("ngIf",n.userLocation),s(2),O("\u{1F465} ",t.capacity||"N/A"," capacity")}}function Pt(l,C){if(l&1){let t=F();i(0,"app-real-time-navigation",59),_("routeUpdated",function(e){y(t);let o=h();return k(o.onNavigationRouteUpdated(e))})("navigationStopped",function(){y(t);let e=h();return k(e.onNavigationStopped())}),a()}if(l&2){let t=h();m("destination",t.navigationDestination)("travelMode",t.selectedTransportMode==="walking"?"foot-walking":t.selectedTransportMode==="cycling"?"cycling-regular":"driving-car")("autoStart",t.isRealTimeNavigationActive)}}function xt(l,C){if(l&1&&(i(0,"p"),c(1),a()),l&2){let t=h();s(),T(t.selectedCenter.address)}}function Ot(l,C){if(l&1&&(i(0,"div",60),d(1,"ion-icon",63),i(2,"span"),c(3),a()()),l&2){let t=h(2);s(3),O("",t.calculateDistanceInKm(t.selectedCenter)," km away")}}function bt(l,C){if(l&1&&(i(0,"div",53)(1,"div",60),d(2,"ion-icon",49),i(3,"span"),c(4),a()(),i(5,"div",60),d(6,"ion-icon",61),i(7,"span"),c(8),a()(),M(9,Ot,4,1,"div",62),a()),l&2){let t=h();s(4),T(t.selectedCenter.address),s(4),O("Capacity: ",t.selectedCenter.capacity||"N/A",""),s(),m("ngIf",t.userLocation)}}function wt(l,C){if(l&1&&(i(0,"span",70),c(1),a()),l&2){let t=h(2);s(),N(" ",(t.routeInfo.walking.distance/1e3).toFixed(1),"km \u2022 ",t.formatTime(t.routeInfo.walking.duration)," ")}}function yt(l,C){l&1&&(i(0,"span",70),c(1,"Calculating..."),a())}function kt(l,C){if(l&1&&(i(0,"span",70),c(1),a()),l&2){let t=h(2);s(),N(" ",(t.routeInfo.cycling.distance/1e3).toFixed(1),"km \u2022 ",t.formatTime(t.routeInfo.cycling.duration)," ")}}function Tt(l,C){l&1&&(i(0,"span",70),c(1,"Calculating..."),a())}function Ft(l,C){if(l&1&&(i(0,"span",70),c(1),a()),l&2){let t=h(2);s(),N(" ",(t.routeInfo.driving.distance/1e3).toFixed(1),"km \u2022 ",t.formatTime(t.routeInfo.driving.duration)," ")}}function Nt(l,C){l&1&&(i(0,"span",70),c(1,"Calculating..."),a())}function St(l,C){if(l&1){let t=F();i(0,"div",64)(1,"h4"),c(2,"Choose Transport Mode:"),a(),i(3,"div",65),_("click",function(){y(t);let e=h();return k(e.navigateWithMode("walking"))}),i(4,"div",37),d(5,"ion-icon",15),a(),i(6,"div",66)(7,"span",67),c(8,"Walking"),a(),M(9,wt,2,2,"span",68)(10,yt,2,0,"span",68),a(),i(11,"div",69),d(12,"ion-icon",57),a()(),i(13,"div",65),_("click",function(){y(t);let e=h();return k(e.navigateWithMode("cycling"))}),i(14,"div",37),d(15,"ion-icon",17),a(),i(16,"div",66)(17,"span",67),c(18,"Cycling"),a(),M(19,kt,2,2,"span",68)(20,Tt,2,0,"span",68),a(),i(21,"div",69),d(22,"ion-icon",57),a()(),i(23,"div",65),_("click",function(){y(t);let e=h();return k(e.navigateWithMode("driving"))}),i(24,"div",37),d(25,"ion-icon",19),a(),i(26,"div",66)(27,"span",67),c(28,"Driving"),a(),M(29,Ft,2,2,"span",68)(30,Nt,2,0,"span",68),a(),i(31,"div",69),d(32,"ion-icon",57),a()()()}if(l&2){let t=h();s(3),b("selected",t.selectedTransportMode==="walking"),s(6),m("ngIf",t.routeInfo.walking),s(),m("ngIf",!t.routeInfo.walking),s(3),b("selected",t.selectedTransportMode==="cycling"),s(6),m("ngIf",t.routeInfo.cycling),s(),m("ngIf",!t.routeInfo.cycling),s(3),b("selected",t.selectedTransportMode==="driving"),s(6),m("ngIf",t.routeInfo.driving),s(),m("ngIf",!t.routeInfo.driving)}}function Lt(l,C){if(l&1&&(i(0,"div",71)(1,"span",72),c(2),a(),i(3,"span",58),c(4),a()()),l&2){let t=h();s(2),T(t.formatTime(t.routeInfo[t.selectedTransportMode||"walking"]==null?null:t.routeInfo[t.selectedTransportMode||"walking"].duration)),s(2),O("",((t.routeInfo[t.selectedTransportMode||"walking"]==null?null:t.routeInfo[t.selectedTransportMode||"walking"].distance)/1e3).toFixed(1)," km")}}var Zt=(()=>{class l{constructor(){this.userMarker=null,this.routeLayer=null,this.nearestMarkers=[],this.evacuationCenters=[],this.userLocation=null,this.newCenterId=null,this.highlightCenter=!1,this.centerLat=null,this.centerLng=null,this.selectedCenter=null,this.selectedTransportMode=null,this.routeInfo={},this.showAllCentersPanel=!1,this.showRouteFooter=!1,this.travelMode="walking",this.routeTime=0,this.routeDistance=0,this.isRealTimeNavigationActive=!1,this.navigationDestination=null,this.currentNavigationRoute=null,this.loadingCtrl=x(lt),this.toastCtrl=x(st),this.alertCtrl=x(rt),this.http=x(W),this.router=x(J),this.route=x(B),this.osmRouting=x(gt),this.mapboxRouting=x(ut),this.enhancedDownload=x(mt)}ngOnInit(){console.log("\u{1F535} FLOOD MAP: Component initialized..."),this.route.queryParams.subscribe(t=>{t.newCenterId&&(this.newCenterId=t.newCenterId,this.highlightCenter=t.highlightCenter==="true",this.centerLat=t.centerLat?parseFloat(t.centerLat):null,this.centerLng=t.centerLng?parseFloat(t.centerLng):null,console.log("\u{1F535} FLOOD MAP: New center to highlight:",this.newCenterId))})}ngAfterViewInit(){return f(this,null,function*(){console.log("\u{1F535} FLOOD MAP: View initialized, loading map..."),setTimeout(()=>f(this,null,function*(){yield this.loadFloodMap()}),100)})}loadFloodMap(){return f(this,null,function*(){let t=yield this.loadingCtrl.create({message:"Loading flood evacuation centers...",spinner:"crescent"});yield t.present();try{let n=yield dt.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),e=n.coords.latitude,o=n.coords.longitude;console.log(`\u{1F535} FLOOD MAP: User location [${e}, ${o}]`),this.userLocation={lat:e,lng:o},this.initializeMap(e,o),yield this.loadFloodCenters(e,o),yield t.dismiss(),yield(yield this.toastCtrl.create({message:`\u{1F535} Showing ${this.evacuationCenters.length} flood evacuation centers`,duration:3e3,color:"primary",position:"top"})).present()}catch(n){yield t.dismiss(),console.error("\u{1F535} FLOOD MAP: Error loading map",n),yield(yield this.alertCtrl.create({header:"Location Error",message:"Unable to get your location. Please enable GPS and try again.",buttons:[{text:"Retry",handler:()=>this.loadFloodMap()},{text:"Go Back",handler:()=>this.router.navigate(["/tabs/home"])}]})).present()}})}initializeMap(t,n){if(console.log(`\u{1F535} FLOOD MAP: Initializing map at [${t}, ${n}]`),!document.getElementById("flood-map"))throw console.error("\u{1F535} FLOOD MAP: Container #flood-map not found!"),new Error("Map container not found. Please ensure the view is properly loaded.");this.map&&this.map.remove(),this.map=p.map("flood-map").setView([t,n],13),this.addTileLayerWithFallback(),this.userMarker=p.marker([t,n],{icon:p.icon({iconUrl:"assets/Location.png",iconSize:[30,30],iconAnchor:[15,30]})}).addTo(this.map),this.userMarker.bindPopup("\u{1F4CD} You are here!").openPopup()}loadFloodCenters(t,n){return f(this,null,function*(){try{console.log("\u{1F535} FLOOD MAP: Fetching flood centers...");let e=[];try{e=(yield A(this.http.get(`${pt.apiUrl}/evacuation-centers`))).data||[],console.log("\u{1F535} FLOOD MAP: Total centers received from API:",e?.length||0)}catch(o){console.error("\u274C API failed:",o),yield(yield this.alertCtrl.create({header:"Connection Error",message:"Cannot connect to server. Please check your internet connection.",buttons:["OK"]})).present();return}if(this.evacuationCenters=e.filter(o=>Array.isArray(o.disaster_type)?o.disaster_type.some(r=>r==="Flood"):o.disaster_type==="Flood"),console.log(`\u{1F535} FLOOD MAP: Filtered to ${this.evacuationCenters.length} flood centers`),console.log("\u{1F535} FLOOD MAP: Filtered centers:",this.evacuationCenters.map(o=>`${o.name} (${JSON.stringify(o.disaster_type)})`)),console.log(`\u{1F535} FLOOD MAP: Filtered to ${this.evacuationCenters.length} flood centers`),this.evacuationCenters.length===0){yield(yield this.alertCtrl.create({header:"No Flood Centers",message:"No flood evacuation centers found in the database.",buttons:["OK"]})).present();return}if(this.evacuationCenters.forEach(o=>{let r=Number(o.latitude),g=Number(o.longitude);if(!isNaN(r)&&!isNaN(g)){let u=p.marker([r,g],{icon:p.icon({iconUrl:"assets/forFlood.png",iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),v=this.calculateDistance(t,n,r,g);u.on("click",()=>{this.openNavigationPanel(o)});let P=this.newCenterId&&o.id.toString()===this.newCenterId;u.bindPopup(`
            <div class="evacuation-popup">
              <h3>\u{1F535} ${o.name} ${P?"\u2B50 NEW!":""}</h3>
              <p><strong>Type:</strong> Flood Center</p>
              <p><strong>Distance:</strong> ${(v/1e3).toFixed(2)} km</p>
              <p><strong>Capacity:</strong> ${o.capacity||"N/A"}</p>
              <p><em>Click marker for route options</em></p>
              ${P?"<p><strong>\u{1F195} Recently Added!</strong></p>":""}
            </div>
          `),P&&(u.openPopup(),this.map.setView([r,g],15),this.toastCtrl.create({message:`\u{1F195} New flood evacuation center: ${o.name}`,duration:5e3,color:"primary",position:"top"}).then(w=>w.present())),u.addTo(this.map),console.log(`\u{1F535} Added flood marker: ${o.name}`)}}),console.log("\u{1F535} Auto-routing to 2 nearest flood centers..."),yield this.routeToTwoNearestCenters(),this.evacuationCenters.length>0){let o=p.latLngBounds([]);o.extend([t,n]),this.evacuationCenters.forEach(r=>{o.extend([Number(r.latitude),Number(r.longitude)])}),this.map.fitBounds(o,{padding:[50,50]})}}catch(e){console.error("\u{1F535} FLOOD MAP: Error loading centers",e),yield(yield this.toastCtrl.create({message:"Error loading flood centers. Please check your connection.",duration:3e3,color:"danger"})).present()}})}routeToTwoNearestCenters(){return f(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){console.log("\u{1F535} FLOOD MAP: No user location or evacuation centers available");return}try{console.log("\u{1F535} FLOOD MAP: Finding 2 nearest flood centers...");let t=this.getTwoNearestCenters(this.userLocation.lat,this.userLocation.lng);if(t.length===0){yield(yield this.toastCtrl.create({message:"No flood evacuation centers found nearby",duration:3e3,color:"warning"})).present();return}this.clearRoutes(),this.addPulsingMarkers(t),yield this.calculateRoutes(t),yield(yield this.toastCtrl.create({message:`\u{1F535} Showing routes to ${t.length} nearest flood centers`,duration:4e3,color:"primary"})).present()}catch(t){console.error("\u{1F535} FLOOD MAP: Error calculating routes",t),yield(yield this.toastCtrl.create({message:"Failed to calculate routes. Please try again.",duration:3e3,color:"danger"})).present()}})}getTwoNearestCenters(t,n){return this.evacuationCenters.map(o=>L(S({},o),{distance:this.calculateDistance(t,n,Number(o.latitude),Number(o.longitude))})).sort((o,r)=>o.distance-r.distance).slice(0,2)}addPulsingMarkers(t){this.nearestMarkers.forEach(n=>this.map.removeLayer(n)),this.nearestMarkers=[],t.forEach((n,e)=>{let o=Number(n.latitude),r=Number(n.longitude);if(!isNaN(o)&&!isNaN(r)){let g=p.divIcon({className:"pulsing-marker",html:`
            <div class="pulse-container">
              <div class="pulse" style="background-color: #0066CC"></div>
              <img src="assets/forFlood.png" class="marker-icon" />
              <div class="marker-label">${e+1}</div>
            </div>
          `,iconSize:[50,50],iconAnchor:[25,50]}),u=p.marker([o,r],{icon:g});u.bindPopup(`
          <div class="evacuation-popup nearest-popup">
            <h3>\u{1F3AF} Nearest Center #${e+1}</h3>
            <h4>${n.name}</h4>
            <p><strong>Type:</strong> Flood</p>
            <p><strong>Distance:</strong> ${(n.distance/1e3).toFixed(2)} km</p>
            <p><strong>Capacity:</strong> ${n.capacity||"N/A"}</p>
          </div>
        `),u.addTo(this.map),this.nearestMarkers.push(u)}})}clearRoutes(){this.map.eachLayer(t=>{t instanceof p.GeoJSON&&this.map.removeLayer(t)})}calculateRoutes(t){return f(this,null,function*(){if(this.userLocation){this.routeLayer=p.layerGroup().addTo(this.map);for(let n=0;n<t.length;n++){let e=t[n],o=Number(e.latitude),r=Number(e.longitude);if(!isNaN(o)&&!isNaN(r))try{console.log(`\u{1F535} FLOOD MAP: Creating Mapbox route to center ${n+1}: ${e.name}`);let g=this.mapboxRouting.convertTravelModeToProfile(this.travelMode),u=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,r,o,g);if(u&&u.routes&&u.routes.length>0){let v=u.routes[0];p.polyline(v.geometry.coordinates.map(w=>[w[1],w[0]]),{color:"#0066CC",weight:4,opacity:.8,dashArray:n===0?void 0:"10, 10"}).addTo(this.routeLayer),n===0&&(this.routeTime=v.duration,this.routeDistance=v.distance),console.log(`\u2705 FLOOD MAP: Added Mapbox route to ${e.name} (${(v.distance/1e3).toFixed(2)}km, ${(v.duration/60).toFixed(0)}min)`)}}catch(g){console.error(`\u{1F535} Error calculating Mapbox route to center ${n+1}:`,g),p.polyline([[this.userLocation.lat,this.userLocation.lng],[o,r]],{color:"#0066CC",weight:4,opacity:.8,dashArray:n===0?void 0:"10, 10"}).addTo(this.routeLayer),console.log(`\u26A0\uFE0F FLOOD MAP: Used fallback straight-line route to ${e.name}`)}}}})}calculateRoute(t,n){return f(this,null,function*(){try{if(!this.userLocation){console.error("\u{1F535} FLOOD MAP: No user location available for routing");return}let e=this.osmRouting.convertTravelModeToProfile(n),o=yield this.osmRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(t.longitude),Number(t.latitude),e);if(o.routes&&o.routes.length>0){let r=o.routes[0],g=this.osmRouting.convertToGeoJSON(r);p.geoJSON(g,{style:{color:"#0066CC",weight:4,opacity:.8}}).addTo(this.map),console.log(`\u{1F535} FLOOD MAP: Route added to ${t.name}`)}}catch(e){console.error("\u{1F535} FLOOD MAP: Error calculating route:",e)}})}showOfflineMarkerInfo(t,n){return f(this,null,function*(){yield(yield this.alertCtrl.create({header:`\u{1F4F1} ${t.name}`,message:`
        <div style="text-align: left;">
          <p><strong>Type:</strong> Flood Center</p>
          <p><strong>Distance:</strong> ${(n/1e3).toFixed(2)} km</p>
          <p><strong>Address:</strong> ${t.address||"N/A"}</p>
          <p><strong>Capacity:</strong> ${t.capacity||"N/A"}</p>
          <p><strong>Status:</strong> ${t.status||"N/A"}</p>
          <br>
          <p><em>\u{1F4F1} Offline Mode: Routing not available. Use external navigation apps for directions.</em></p>
        </div>
      `,buttons:[{text:"Open in Maps",handler:()=>{this.openInExternalMaps(t)}},{text:"Close",role:"cancel"}]})).present()})}openInExternalMaps(t,n){return f(this,null,function*(){let e=Number(t.latitude),o=Number(t.longitude),r="walking";n==="driving"?r="driving":n==="cycling"&&(r="bicycling");let g=`https://www.google.com/maps/dir/?api=1&destination=${e},${o}&travelmode=${r}`;try{window.open(g,"_system")}catch(u){console.error("Error opening external maps:",u),yield(yield this.toastCtrl.create({message:"Could not open external maps app",duration:3e3,color:"danger"})).present()}})}openNavigationPanel(t){return f(this,null,function*(){this.selectedCenter=t,this.selectedTransportMode=null,this.routeInfo={},yield this.calculateAllRoutes(t)})}closeNavigationPanel(){this.selectedCenter=null,this.selectedTransportMode=null,this.showRouteFooter=!1,this.routeInfo={}}selectTransportMode(t){this.selectedTransportMode=t,this.selectedCenter&&this.showRouteOnMap(this.selectedCenter,t)}calculateAllRoutes(t){return f(this,null,function*(){if(!this.userLocation)return;let n=["walking","cycling","driving"];for(let e of n)try{let o=this.osmRouting.convertTravelModeToProfile(e),r=yield this.osmRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(t.longitude),Number(t.latitude),o);if(r.routes&&r.routes.length>0){let g=r.routes[0];this.routeInfo[e]={duration:g.duration,distance:g.distance}}}catch(o){console.error(`Error calculating ${e} route:`,o)}})}showRouteOnMap(t,n){return f(this,null,function*(){if(this.userLocation){this.clearRoutes();try{let e=this.osmRouting.convertTravelModeToProfile(n),o=yield this.osmRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(t.longitude),Number(t.latitude),e);if(o.routes&&o.routes.length>0){let r=o.routes[0],g=this.osmRouting.convertToGeoJSON(r);p.geoJSON(g,{style:{color:"#0066CC",weight:4,opacity:.8}}).addTo(this.map)}}catch(e){console.error("Error showing route on map:",e)}}})}startNavigation(){return f(this,null,function*(){if(!this.selectedCenter||!this.selectedTransportMode)return;yield this.routeToCenter(this.selectedCenter,this.selectedTransportMode),this.closeNavigationPanel(),yield(yield this.toastCtrl.create({message:`\u{1F9ED} Navigation started to ${this.selectedCenter.name}`,duration:3e3,color:"primary",position:"top"})).present()})}formatTime(t){if(!t)return"";let n=Math.round(t/60);if(n<60)return`${n}m`;{let e=Math.floor(n/60),o=n%60;return`${e}h ${o}m`}}formatDistance(t){return t?t<1e3?`${Math.round(t)}m`:`${(t/1e3).toFixed(1)}km`:""}routeToCenter(t,n){return f(this,null,function*(){if(this.userLocation)try{this.clearRoutes();let e=this.osmRouting.convertTravelModeToProfile(n),o=yield this.osmRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(t.longitude),Number(t.latitude),e);if(o&&o.routes&&o.routes.length>0){let r=o.routes[0],u=p.polyline(r.geometry.coordinates.map(P=>[P[1],P[0]]),{color:"#0066CC",weight:5,opacity:.8});u.addTo(this.map),yield(yield this.toastCtrl.create({message:`\u{1F535} Route: ${(r.distance/1e3).toFixed(2)}km, ${(r.duration/60).toFixed(0)}min via ${n}`,duration:4e3,color:"primary"})).present(),this.map.fitBounds(u.getBounds(),{padding:[50,50]})}}catch(e){console.error("\u{1F535} FLOOD MAP: Error calculating individual route:",e),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}})}calculateDistance(t,n,e,o){let g=t*Math.PI/180,u=e*Math.PI/180,v=(e-t)*Math.PI/180,P=(o-n)*Math.PI/180,w=Math.sin(v/2)*Math.sin(v/2)+Math.cos(g)*Math.cos(u)*Math.sin(P/2)*Math.sin(P/2);return 6371e3*(2*Math.atan2(Math.sqrt(w),Math.sqrt(1-w)))}goBack(){this.router.navigate(["/tabs/home"])}onTravelModeChange(t){let n=t.detail.value;this.changeTravelMode(n)}changeTravelMode(t){return f(this,null,function*(){this.travelMode=t,yield(yield this.toastCtrl.create({message:`\u{1F535} Travel mode changed to ${t}`,duration:2e3,color:"primary"})).present(),this.userLocation&&this.evacuationCenters.length>0&&(yield this.routeToTwoNearestCenters())})}downloadMap(){return f(this,null,function*(){if(!this.map){yield(yield this.toastCtrl.create({message:"Map not loaded yet. Please wait and try again.",duration:3e3,color:"warning"})).present();return}try{yield this.enhancedDownload.downloadMapWithRoutes("flood-map",this.map,"Flood",!0)}catch(t){console.error("Enhanced download error:",t),yield(yield this.toastCtrl.create({message:"Failed to download map. Please try again.",duration:3e3,color:"danger"})).present()}})}showAllCenters(){this.showAllCentersPanel=!0}closeAllCentersPanel(){this.showAllCentersPanel=!1}routeToNearestCenters(){return f(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){yield(yield this.toastCtrl.create({message:"Unable to calculate routes. Please ensure location is available.",duration:3e3,color:"warning"})).present();return}try{yield(yield this.toastCtrl.create({message:"\u{1F30A} Calculating routes to nearest flood centers...",duration:2e3,color:"primary"})).present(),yield this.routeToTwoNearestCenters()}catch(t){console.error("\u{1F30A} Error routing to nearest centers:",t),yield(yield this.toastCtrl.create({message:"Failed to calculate routes. Please try again.",duration:3e3,color:"danger"})).present()}})}calculateDistanceInKm(t){if(!this.userLocation)return"N/A";let n=Number(t.latitude),e=Number(t.longitude);return isNaN(n)||isNaN(e)?"N/A":(this.calculateDistance(this.userLocation.lat,this.userLocation.lng,n,e)/1e3).toFixed(1)}showNavigationPanel(t){return f(this,null,function*(){this.selectedCenter=t,this.selectedTransportMode="walking",this.routeInfo={},this.showRouteFooter=!0,yield this.calculateAllRoutes(t)})}selectCenterFromList(t){this.closeAllCentersPanel(),this.showNavigationPanel(t);let n=Number(t.latitude),e=Number(t.longitude);this.map.setView([n,e],15)}navigateWithMode(t){this.selectedTransportMode=t,this.showRouteFooter=!0,this.selectedCenter&&this.showRouteOnMap(this.selectedCenter,t)}addTileLayerWithFallback(){let t=[{url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",attribution:"\xA9 OpenStreetMap contributors",name:"OpenStreetMap"},{url:"https://cartodb-basemaps-{s}.global.ssl.fastly.net/light_all/{z}/{x}/{y}.png",attribution:"\xA9 OpenStreetMap contributors, \xA9 CartoDB",name:"CartoDB Light"},{url:"https://{s}.tile.openstreetmap.fr/osmfr/{z}/{x}/{y}.png",attribution:"\xA9 OpenStreetMap contributors, \xA9 OpenStreetMap France",name:"OpenStreetMap France"}],n=0,e=null,o=()=>{if(n>=t.length){console.error("\u{1F535} FLOOD MAP: All tile providers failed, using offline placeholder"),this.addOfflinePlaceholder();return}let r=t[n];console.log(`\u{1F535} FLOOD MAP: Trying tile provider: ${r.name}`),e&&this.map.removeLayer(e),e=p.tileLayer(r.url,{attribution:r.attribution,maxZoom:19,errorTileUrl:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="}),e.on("tileerror",g=>{console.warn(`\u{1F535} FLOOD MAP: Tile error with ${r.name}:`,g),n++,setTimeout(o,1e3)}),e.on("tileload",()=>{console.log(`\u{1F535} FLOOD MAP: Successfully loaded tiles from ${r.name}`)}),e.addTo(this.map)};o()}addOfflinePlaceholder(){let t=document.createElement("canvas");t.width=256,t.height=256;let n=t.getContext("2d");n&&(n.fillStyle="#f0f0f0",n.fillRect(0,0,256,256),n.fillStyle="#999",n.font="14px Arial",n.textAlign="center",n.fillText("Map tiles",128,120),n.fillText("unavailable",128,140));let e=t.toDataURL();p.tileLayer(e,{attribution:"Offline Mode - Map tiles unavailable"}).addTo(this.map)}ionViewWillLeave(){this.isRealTimeNavigationActive&&this.osmRouting.stopRealTimeRouting(),this.map&&this.map.remove()}startRealTimeNavigation(t){console.log("\u{1F9ED} Starting real-time navigation to flood center:",t.name),this.navigationDestination={lat:Number(t.latitude),lng:Number(t.longitude),name:t.name},this.isRealTimeNavigationActive=!0,this.toastCtrl.create({message:`\u{1F9ED} Real-time navigation started to ${t.name}`,duration:3e3,color:"primary"}).then(n=>n.present())}onNavigationRouteUpdated(t){console.log("\u{1F504} Flood map navigation route updated"),this.currentNavigationRoute=t,this.updateMapWithNavigationRoute(t)}onNavigationStopped(){console.log("\u23F9\uFE0F Flood map real-time navigation stopped"),this.isRealTimeNavigationActive=!1,this.navigationDestination=null,this.currentNavigationRoute=null,this.clearNavigationRoute(),this.toastCtrl.create({message:"\u23F9\uFE0F Navigation stopped",duration:2e3,color:"medium"}).then(t=>t.present())}updateMapWithNavigationRoute(t){if(this.clearNavigationRoute(),t.geometry&&t.geometry.coordinates){let n=this.osmRouting.convertToGeoJSON(t),e=p.geoJSON(n,{style:{color:"#17a2b8",weight:6,opacity:.8,dashArray:"10, 5"}}).addTo(this.map);e.isNavigationRoute=!0}}clearNavigationRoute(){this.map.eachLayer(t=>{t.isNavigationRoute&&this.map.removeLayer(t)})}static{this.\u0275fac=function(n){return new(n||l)}}static{this.\u0275cmp=I({type:l,selectors:[["app-flood-map"]],standalone:!0,features:[$],decls:67,vars:24,consts:[[3,"translucent"],["color","primary"],["slot","start"],["fill","clear",3,"click"],["name","arrow-back-outline","color","light"],[3,"fullscreen"],["id","flood-map",2,"height","100%","width","100%"],[1,"map-controls"],["fill","clear",1,"control-btn",3,"click"],["src","assets/home-insuranceForFlood.png","alt","All Centers",1,"control-icon"],["src","assets/downloadForFlood.png","alt","Download",1,"control-icon"],["src","assets/compassForFlood.png","alt","Route to Nearest",1,"control-icon"],[1,"travel-mode-selector"],[3,"ngModelChange","ionChange","ngModel"],["value","walking"],["name","walk-outline"],["value","cycling"],["name","bicycle-outline"],["value","driving"],["name","car-outline"],["class","route-info",4,"ngIf"],[1,"all-centers-panel"],[1,"panel-content"],[1,"panel-header"],[1,"header-info"],["name","close"],[1,"centers-list"],["class","center-item",3,"click",4,"ngFor","ngForOf"],[1,"all-centers-overlay",3,"click"],[3,"destination","travelMode","autoStart","routeUpdated","navigationStopped",4,"ngIf"],[1,"navigation-panel"],[4,"ngIf"],["class","center-details",4,"ngIf"],["class","transport-options",4,"ngIf"],[1,"route-footer"],[1,"footer-content"],[1,"route-summary"],[1,"transport-icon"],[3,"name"],[1,"route-details"],[1,"destination"],["class","route-info-footer",4,"ngIf"],[1,"footer-actions"],["fill","solid","color","primary","size","small",3,"click"],[1,"navigation-overlay",3,"click"],[1,"route-info"],[1,"route-header"],["name","time-outline","color","primary"],[1,"route-item"],["name","location-outline"],[1,"center-item",3,"click"],[1,"center-info"],[1,"address"],[1,"center-details"],["class","distance",4,"ngIf"],[1,"capacity"],[1,"center-actions"],["name","chevron-forward-outline"],[1,"distance"],[3,"routeUpdated","navigationStopped","destination","travelMode","autoStart"],[1,"detail-item"],["name","people-outline"],["class","detail-item",4,"ngIf"],["name","navigate-outline"],[1,"transport-options"],[1,"transport-option",3,"click"],[1,"transport-info"],[1,"mode"],["class","details",4,"ngIf"],[1,"transport-action"],[1,"details"],[1,"route-info-footer"],[1,"time"]],template:function(n,e){n&1&&(i(0,"ion-header",0)(1,"ion-toolbar",1)(2,"ion-buttons",2)(3,"ion-button",3),_("click",function(){return e.goBack()}),d(4,"ion-icon",4),a()()()(),i(5,"ion-content",5),d(6,"div",6),i(7,"div",7)(8,"ion-button",8),_("click",function(){return e.showAllCenters()}),d(9,"img",9),a(),i(10,"ion-button",8),_("click",function(){return e.downloadMap()}),d(11,"img",10),a(),i(12,"ion-button",8),_("click",function(){return e.routeToNearestCenters()}),d(13,"img",11),a()(),i(14,"div",12)(15,"ion-segment",13),R("ngModelChange",function(r){return D(e.travelMode,r)||(e.travelMode=r),r}),_("ionChange",function(r){return e.onTravelModeChange(r)}),i(16,"ion-segment-button",14),d(17,"ion-icon",15),i(18,"ion-label"),c(19,"Walk"),a()(),i(20,"ion-segment-button",16),d(21,"ion-icon",17),i(22,"ion-label"),c(23,"Cycle"),a()(),i(24,"ion-segment-button",18),d(25,"ion-icon",19),i(26,"ion-label"),c(27,"Drive"),a()()()(),M(28,Ct,16,3,"div",20),i(29,"div",21)(30,"div",22)(31,"div",23)(32,"div",24)(33,"h3"),c(34,"\u{1F30A} Flood Evacuation Centers"),a(),i(35,"p"),c(36),a()(),i(37,"ion-button",3),_("click",function(){return e.closeAllCentersPanel()}),d(38,"ion-icon",25),a()(),i(39,"div",26),M(40,Mt,12,4,"div",27),a()()(),i(41,"div",28),_("click",function(){return e.closeAllCentersPanel()}),a(),M(42,Pt,1,3,"app-real-time-navigation",29),i(43,"div",30)(44,"div",22)(45,"div",23)(46,"div",24)(47,"h3"),c(48),a(),M(49,xt,2,1,"p",31),a(),i(50,"ion-button",3),_("click",function(){return e.closeNavigationPanel()}),d(51,"ion-icon",25),a()(),M(52,bt,10,3,"div",32)(53,St,33,12,"div",33),a()(),i(54,"div",34)(55,"div",35)(56,"div",36)(57,"div",37),d(58,"ion-icon",38),a(),i(59,"div",39)(60,"div",40),c(61),a(),M(62,Lt,5,2,"div",41),a()(),i(63,"div",42)(64,"ion-button",43),_("click",function(){return e.startRealTimeNavigation(e.selectedCenter)}),c(65," Start "),a()()()(),i(66,"div",44),_("click",function(){return e.closeNavigationPanel()}),a()()),n&2&&(m("translucent",!0),s(5),m("fullscreen",!0),s(10),E("ngModel",e.travelMode),s(13),m("ngIf",e.routeTime>0&&e.routeDistance>0),s(),b("show",e.showAllCentersPanel),s(7),O("",e.evacuationCenters.length," centers available"),s(4),m("ngForOf",e.evacuationCenters),s(),b("show",e.showAllCentersPanel),s(),m("ngIf",e.navigationDestination),s(),b("show",e.selectedCenter),s(5),O("\u{1F30A} ",e.selectedCenter==null?null:e.selectedCenter.name,""),s(),m("ngIf",e.selectedCenter),s(3),m("ngIf",e.selectedCenter),s(),m("ngIf",e.selectedCenter),s(),b("show",e.showRouteFooter&&e.selectedCenter),s(4),m("name",e.selectedTransportMode==="walking"?"walk-outline":e.selectedTransportMode==="cycling"?"bicycle-outline":"car-outline"),s(3),T(e.selectedCenter==null?null:e.selectedCenter.name),s(),m("ngIf",e.routeInfo[e.selectedTransportMode||"walking"]),s(4),b("show",e.selectedCenter))},dependencies:[ct,q,H,Q,Z,X,tt,et,nt,ot,it,at,Y,U,z,V,K,G,j,ht],styles:["#flood-map[_ngcontent-%COMP%]{height:100%;width:100%;z-index:1}.map-controls[_ngcontent-%COMP%]{position:absolute;top:80px;right:10px;z-index:1000;display:flex;flex-direction:column;gap:8px}.control-btn[_ngcontent-%COMP%]{--background: rgba(255, 255, 255, .95);--color: #3880ff;--border-radius: 12px;width:50px;height:50px;box-shadow:0 4px 12px #00000026;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(56,128,255,.2)}.control-btn[_ngcontent-%COMP%]:hover{--background: rgba(56, 128, 255, .1);transform:translateY(-2px);box-shadow:0 6px 16px #0003}.control-icon[_ngcontent-%COMP%]{width:28px;height:28px;object-fit:contain}.all-centers-panel[_ngcontent-%COMP%]{position:fixed;top:0;right:-400px;width:380px;height:100vh;background:#fff;box-shadow:-4px 0 20px #00000026;z-index:1500;transition:right .3s cubic-bezier(.25,.46,.45,.94);display:flex;flex-direction:column}.all-centers-panel.show[_ngcontent-%COMP%]{right:0}.all-centers-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;padding:20px;overflow:hidden}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:20px;padding-bottom:16px;border-bottom:2px solid var(--ion-color-primary-tint)}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]{flex:1}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-size:20px;font-weight:700;color:var(--ion-color-primary);line-height:1.2}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--ion-color-medium);font-weight:500}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);margin:0}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding-right:4px}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:var(--ion-color-light);border-radius:2px}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:var(--ion-color-primary-tint);border-radius:2px}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;margin-bottom:12px;background:#fff;border-radius:12px;border:1px solid var(--ion-color-light);box-shadow:0 2px 8px #00000014;cursor:pointer;transition:all .2s ease}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 16px #3880ff26;border-color:var(--ion-color-primary-tint)}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]{flex:1}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 4px;font-size:16px;font-weight:600;color:var(--ion-color-dark);line-height:1.3}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .address[_ngcontent-%COMP%]{margin:0 0 8px;font-size:13px;color:var(--ion-color-medium);line-height:1.4}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%], .all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .capacity[_ngcontent-%COMP%]{font-size:12px;font-weight:500;padding:2px 8px;border-radius:8px;background:var(--ion-color-primary-tint);color:var(--ion-color-primary);width:fit-content}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-actions[_ngcontent-%COMP%]{margin-left:12px}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-actions[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px;color:var(--ion-color-medium)}.all-centers-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background:#0006;z-index:1400;opacity:0;visibility:hidden;transition:all .3s ease}.all-centers-overlay.show[_ngcontent-%COMP%]{opacity:1;visibility:visible}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: #3880ff;--color: white;--border-width: 0}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;font-size:18px}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.floating-info[_ngcontent-%COMP%]{position:absolute;top:20px;right:20px;z-index:1000;max-width:250px}.floating-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.floating-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-primary);margin-bottom:4px}.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.floating-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium);line-height:1.3}ion-toolbar[_ngcontent-%COMP%]{--background: var(--ion-color-primary);--color: white}ion-title[_ngcontent-%COMP%]{font-weight:600}.navigation-panel[_ngcontent-%COMP%]{position:fixed;top:0;right:-400px;width:380px;height:100vh;background:#fff;z-index:2000;transition:right .3s ease-in-out;box-shadow:-4px 0 20px #00000026}.navigation-panel.show[_ngcontent-%COMP%]{right:0}.navigation-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;padding:60px 20px 20px}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:24px;padding-bottom:16px;border-bottom:1px solid var(--ion-color-light)}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]{flex:1}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-size:18px;font-weight:600;color:var(--ion-color-dark);line-height:1.2}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--ion-color-medium);line-height:1.3}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);margin:0}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]{flex:1}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .option-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:16px;font-weight:600;color:var(--ion-color-primary)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .option-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-buttons[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;margin-bottom:24px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;border:2px solid var(--ion-color-light);border-radius:12px;background:#fff;cursor:pointer;transition:all .2s ease;position:relative}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]:hover{border-color:var(--ion-color-primary-tint);background:var(--ion-color-primary-tint)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn.active[_ngcontent-%COMP%]{border-color:var(--ion-color-primary);background:var(--ion-color-primary-tint)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:var(--ion-color-primary)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;margin-right:12px;color:var(--ion-color-medium);transition:color .2s ease}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:var(--ion-color-dark);flex:1}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-end;gap:2px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:var(--ion-color-primary)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]{width:100%;padding:16px;background:var(--ion-color-primary);color:#fff;border:none;border-radius:12px;font-size:16px;font-weight:600;display:flex;align-items:center;justify-content:center;gap:8px;cursor:pointer;transition:background .2s ease}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]:hover{background:var(--ion-color-primary-shade)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}@media (max-width: 768px){.map-controls[_ngcontent-%COMP%]{top:90px;left:8px}}[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse-container[_ngcontent-%COMP%]{position:relative;width:50px;height:50px;display:flex;align-items:center;justify-content:center}[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse[_ngcontent-%COMP%]{position:absolute;width:40px;height:40px;border-radius:50%;animation:_ngcontent-%COMP%_pulse 2s infinite;opacity:.6}[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-icon[_ngcontent-%COMP%]{width:30px;height:30px;z-index:2;position:relative}[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-label[_ngcontent-%COMP%]{position:absolute;top:-8px;right:-8px;background:#06c;color:#fff;border-radius:50%;width:20px;height:20px;display:flex;align-items:center;justify-content:center;font-size:12px;font-weight:700;z-index:3;border:2px solid white}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(.8);opacity:.8}50%{transform:scale(1.2);opacity:.4}to{transform:scale(.8);opacity:.8}}.travel-mode-selector[_ngcontent-%COMP%]{position:absolute;top:20px;left:20px;z-index:1000;background:#fffffff2;border-radius:12px;padding:8px;box-shadow:0 2px 8px #0003}.travel-mode-selector[_ngcontent-%COMP%]   ion-segment[_ngcontent-%COMP%]{--background: transparent;min-height:40px}.travel-mode-selector[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);--color-checked: var(--ion-color-primary);--indicator-color: var(--ion-color-primary);min-height:40px}.travel-mode-selector[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px;margin-bottom:2px}.travel-mode-selector[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:12px;font-weight:500}.route-info[_ngcontent-%COMP%]{position:absolute;bottom:20px;left:20px;z-index:1000;max-width:200px}.route-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.route-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.route-info[_ngcontent-%COMP%]   .route-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-primary);margin-bottom:8px}.route-info[_ngcontent-%COMP%]   .route-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.route-info[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.route-info[_ngcontent-%COMP%]   .route-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:14px;color:var(--ion-color-dark)}.route-info[_ngcontent-%COMP%]   .route-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px;color:var(--ion-color-primary)}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]{text-align:center;min-width:200px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:var(--ion-color-primary);font-size:16px;font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0;font-size:14px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:var(--ion-color-dark)}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup.nearest-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#06c;font-size:18px}"]})}}return l})();export{Zt as FloodMapPage};
