import{a as at}from"./chunk-GEGX2RE6.js";import{a as it,c as ct}from"./chunk-YZWAW4SM.js";import{a as rt}from"./chunk-QZ5PEPJK.js";import{b as nt}from"./chunk-I4SN7ED3.js";import{a as ot}from"./chunk-LSM7X32V.js";import"./chunk-3J7GGTVR.js";import{a as et}from"./chunk-WPPT3EJF.js";import"./chunk-2LL5MXLB.js";import{B as l,Eb as G,F as x,Fb as J,G as C,Gb as H,I as T,J as i,Jb as Y,K as a,L as f,Mb as K,O as A,P,Q as _,X as r,Y as h,Z as N,bc as Q,ca as z,d as D,dc as X,fc as Z,gc as tt,ib as U,l as M,m as R,na as F,pa as $,r as w,ra as V,s as k,wb as W,ya as q,yb as j,zb as B}from"./chunk-SFXIJNIZ.js";import"./chunk-LR6AIEJQ.js";import"./chunk-6NVMNNPA.js";import"./chunk-KW2BML7M.js";import"./chunk-SV2ZKNWA.js";import"./chunk-YJDO75HI.js";import"./chunk-HC6MZPB3.js";import"./chunk-Q5Q6EGIP.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-ZJ5IMUT4.js";import"./chunk-SGSBBWFA.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-EI2QJP5N.js";import"./chunk-W6U2AR23.js";import"./chunk-APL3YEA6.js";import"./chunk-HSXX7Y3C.js";import"./chunk-FUGLTCJS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import{a as E,b as I,f as lt,g as v}from"./chunk-2R6CW7ES.js";var u=lt(ct());function pt(p,b){if(p&1){let t=A();i(0,"app-real-time-navigation",27),P("routeUpdated",function(o){w(t);let e=_();return k(e.onNavigationRouteUpdated(o))})("navigationStopped",function(){w(t);let o=_();return k(o.onNavigationStopped())}),a()}if(p&2){let t=_();C("destination",t.navigationDestination)("travelMode",t.selectedTransportMode==="walking"?"foot-walking":t.selectedTransportMode==="cycling"?"cycling-regular":"driving-car")("autoStart",t.isRealTimeNavigationActive)}}function dt(p,b){if(p&1&&(i(0,"div",28)(1,"ion-card")(2,"ion-card-content")(3,"div",29),f(4,"ion-icon",30),i(5,"span"),r(6,"Route to Nearest Center"),a()(),i(7,"div",31)(8,"div",32),f(9,"ion-icon",33),i(10,"span"),r(11),a()(),i(12,"div",32),f(13,"ion-icon",34),i(14,"span"),r(15),a()()()()()()),p&2){let t=_();l(9),C("name",t.travelMode==="walking"?"walk-outline":t.travelMode==="cycling"?"bicycle-outline":"car-outline"),l(2),N("",(t.routeTime/60).toFixed(0)," min"),l(4),N("",(t.routeDistance/1e3).toFixed(2)," km")}}function gt(p,b){if(p&1&&(i(0,"div",35)(1,"h3"),r(2),a(),i(3,"p"),r(4),a()()),p&2){let t=_();l(2),h(t.selectedCenter.name),l(2),h(t.selectedCenter.address)}}function ut(p,b){if(p&1&&(i(0,"div",28)(1,"span",44),r(2),a(),i(3,"span",45),r(4),a()()),p&2){let t=_(2);l(2),h(t.formatTime(t.routeInfo.walking==null?null:t.routeInfo.walking.duration)),l(2),h(t.formatDistance(t.routeInfo.walking==null?null:t.routeInfo.walking.distance))}}function mt(p,b){if(p&1&&(i(0,"div",28)(1,"span",44),r(2),a(),i(3,"span",45),r(4),a()()),p&2){let t=_(2);l(2),h(t.formatTime(t.routeInfo.cycling==null?null:t.routeInfo.cycling.duration)),l(2),h(t.formatDistance(t.routeInfo.cycling==null?null:t.routeInfo.cycling.distance))}}function ht(p,b){if(p&1&&(i(0,"div",28)(1,"span",44),r(2),a(),i(3,"span",45),r(4),a()()),p&2){let t=_(2);l(2),h(t.formatTime(t.routeInfo.driving==null?null:t.routeInfo.driving.duration)),l(2),h(t.formatDistance(t.routeInfo.driving==null?null:t.routeInfo.driving.distance))}}function ft(p,b){if(p&1){let t=A();i(0,"button",46),P("click",function(){w(t);let o=_(2);return k(o.startNavigation())}),f(1,"ion-icon",47),i(2,"span"),r(3,"Start Navigation"),a()()}}function _t(p,b){if(p&1){let t=A();i(0,"div",36)(1,"div",37),f(2,"ion-icon",25),i(3,"span"),r(4,"Choose Transportation"),a()(),i(5,"div",38)(6,"button",39),P("click",function(){w(t);let o=_();return k(o.selectTransportMode("walking"))}),f(7,"ion-icon",40),i(8,"span"),r(9,"Walk"),a(),x(10,ut,5,2,"div",15),a(),i(11,"button",39),P("click",function(){w(t);let o=_();return k(o.selectTransportMode("cycling"))}),f(12,"ion-icon",41),i(13,"span"),r(14,"Cycle"),a(),x(15,mt,5,2,"div",15),a(),i(16,"button",39),P("click",function(){w(t);let o=_();return k(o.selectTransportMode("driving"))}),f(17,"ion-icon",42),i(18,"span"),r(19,"Drive"),a(),x(20,ht,5,2,"div",15),a()(),x(21,ft,4,0,"button",43),a()}if(p&2){let t=_();l(6),T("active",t.selectedTransportMode==="walking"),l(4),C("ngIf",t.routeInfo&&t.selectedTransportMode==="walking"),l(),T("active",t.selectedTransportMode==="cycling"),l(4),C("ngIf",t.routeInfo&&t.selectedTransportMode==="cycling"),l(),T("active",t.selectedTransportMode==="driving"),l(4),C("ngIf",t.routeInfo&&t.selectedTransportMode==="driving"),l(),C("ngIf",t.selectedTransportMode&&t.routeInfo)}}var Dt=(()=>{class p{constructor(){this.userMarker=null,this.routeLayer=null,this.nearestMarkers=[],this.evacuationCenters=[],this.centerCounts={earthquake:0,typhoon:0,flood:0,fire:0,landslide:0,others:0,multiple:0,total:0},this.travelMode="walking",this.routeTime=0,this.routeDistance=0,this.userLocation=null,this.selectedCenter=null,this.selectedTransportMode=null,this.routeInfo={},this.isRealTimeNavigationActive=!1,this.navigationDestination=null,this.currentNavigationRoute=null,this.loadingCtrl=M(X),this.toastCtrl=M(Z),this.alertCtrl=M(Q),this.http=M(V),this.router=M(q),this.osmRouting=M(ot),this.mapboxRouting=M(it),this.enhancedDownload=M(at)}ngOnInit(){return v(this,null,function*(){console.log("\u{1F5FA}\uFE0F ALL MAPS: Initializing..."),yield this.loadAllMaps()})}loadAllMaps(){return v(this,null,function*(){let t=yield this.loadingCtrl.create({message:"Loading all evacuation centers...",spinner:"crescent"});yield t.present();try{let n=yield nt.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),o=n.coords.latitude,e=n.coords.longitude;this.userLocation={lat:o,lng:e},console.log(`\u{1F5FA}\uFE0F ALL MAPS: User location [${o}, ${e}]`),this.initializeMap(o,e),yield this.loadAllCenters(o,e),yield t.dismiss(),yield(yield this.toastCtrl.create({message:`\u{1F5FA}\uFE0F Showing all ${this.centerCounts.total} evacuation centers`,duration:3e3,color:"secondary",position:"top"})).present()}catch(n){yield t.dismiss(),console.error("\u{1F5FA}\uFE0F ALL MAPS: Error loading map",n),yield(yield this.alertCtrl.create({header:"Location Error",message:"Unable to get your location. Please enable GPS and try again.",buttons:[{text:"Retry",handler:()=>this.loadAllMaps()},{text:"Go Back",handler:()=>this.router.navigate(["/tabs/home"])}]})).present()}})}initializeMap(t,n){console.log(`\u{1F5FA}\uFE0F ALL MAPS: Initializing map at [${t}, ${n}]`),this.map&&this.map.remove(),this.map=u.map("all-maps").setView([t,n],12),u.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors"}).addTo(this.map),this.userMarker=u.marker([t,n],{icon:u.icon({iconUrl:"assets/icons/user-location.png",iconSize:[30,30],iconAnchor:[15,30]})}).addTo(this.map),this.userMarker.bindPopup("\u{1F4CD} You are here!").openPopup()}loadAllCenters(t,n){return v(this,null,function*(){try{console.log("\u{1F5FA}\uFE0F ALL MAPS: Fetching all evacuation centers...");let o=yield D(this.http.get(`${et.apiUrl}/evacuation-centers`));if(console.log("\u{1F5FA}\uFE0F ALL MAPS: Total centers received:",o.data?.length||0),this.evacuationCenters=o.data||[],this.centerCounts.earthquake=this.evacuationCenters.filter(e=>Array.isArray(e.disaster_type)?e.disaster_type.includes("Earthquake"):e.disaster_type==="Earthquake").length,this.centerCounts.typhoon=this.evacuationCenters.filter(e=>Array.isArray(e.disaster_type)?e.disaster_type.includes("Typhoon"):e.disaster_type==="Typhoon").length,this.centerCounts.flood=this.evacuationCenters.filter(e=>Array.isArray(e.disaster_type)?e.disaster_type.includes("Flood"):e.disaster_type==="Flood"||e.disaster_type==="Flash Flood").length,this.centerCounts.fire=this.evacuationCenters.filter(e=>Array.isArray(e.disaster_type)?e.disaster_type.includes("Fire"):e.disaster_type==="Fire").length,this.centerCounts.landslide=this.evacuationCenters.filter(e=>Array.isArray(e.disaster_type)?e.disaster_type.includes("Landslide"):e.disaster_type==="Landslide").length,this.centerCounts.others=this.evacuationCenters.filter(e=>Array.isArray(e.disaster_type)?e.disaster_type.some(g=>g==="Others"||typeof g=="string"&&g.startsWith("Others:")):e.disaster_type==="Others"||typeof e.disaster_type=="string"&&e.disaster_type.startsWith("Others:")).length,this.centerCounts.multiple=this.evacuationCenters.filter(e=>Array.isArray(e.disaster_type)?e.disaster_type.length>1:!1).length,this.centerCounts.total=this.evacuationCenters.length,console.log("\u{1F5FA}\uFE0F ALL MAPS: Center counts:",this.centerCounts),this.evacuationCenters.length===0){yield(yield this.alertCtrl.create({header:"No Evacuation Centers",message:"No evacuation centers found in the database.",buttons:["OK"]})).present();return}if(this.evacuationCenters.forEach(e=>{let g=Number(e.latitude),d=Number(e.longitude);if(!isNaN(g)&&!isNaN(d)){let s="assets/Location.png",c="\u26AA",m=Array.isArray(e.disaster_type)?e.disaster_type:[e.disaster_type];if(m.length>1)s="assets/forMultiple.png",c="\u{1F518}",console.log(`\u{1F5FA}\uFE0F Multi-type center: ${e.name} supports ${m.join(", ")}`);else{let L=Array.isArray(e.disaster_type)?e.disaster_type[0]:e.disaster_type;if(typeof L=="string"&&L.startsWith("Others:"))s="assets/forOthers.png",c="\u{1F7E3}";else switch(L){case"Earthquake":s="assets/forEarthquake.png",c="\u{1F7E0}";break;case"Typhoon":s="assets/forTyphoon.png",c="\u{1F7E2}";break;case"Flood":s="assets/forFlood.png",c="\u{1F535}";break;case"Fire":s="assets/forFire.png",c="\u{1F534}";break;case"Landslide":s="assets/forLandslide.png",c="\u{1F7E4}";break;case"Others":s="assets/forOthers.png",c="\u{1F7E3}";break;default:s="assets/forOthers.png",c="\u{1F7E3}";break}}let y=u.marker([g,d],{icon:u.icon({iconUrl:s,iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),S=this.calculateDistance(t,n,g,d);y.on("click",()=>{this.showNavigationPanel(e)});let st=Array.isArray(e.disaster_type)?e.disaster_type.join(", "):e.disaster_type||"General";y.bindPopup(`
            <div class="evacuation-popup">
              <h3>${c} ${e.name}</h3>
              <p><strong>Type:</strong> ${st}</p>
              <p><strong>Distance:</strong> ${(S/1e3).toFixed(2)} km</p>
              <p><strong>Capacity:</strong> ${e.capacity||"N/A"}</p>
              <p><em>Click marker for route options</em></p>
            </div>
          `),y.addTo(this.map),console.log(`\u{1F5FA}\uFE0F Added ${e.disaster_type} marker: ${e.name}`)}}),this.evacuationCenters.length>0){let e=u.latLngBounds([]);e.extend([t,n]),this.evacuationCenters.forEach(g=>{e.extend([Number(g.latitude),Number(g.longitude)])}),this.map.fitBounds(e,{padding:[50,50]})}}catch(o){console.error("\u{1F5FA}\uFE0F ALL MAPS: Error loading centers",o),yield(yield this.toastCtrl.create({message:"Error loading evacuation centers. Please check your connection.",duration:3e3,color:"danger"})).present()}})}calculateDistance(t,n,o,e){let d=t*Math.PI/180,s=o*Math.PI/180,c=(o-t)*Math.PI/180,m=(e-n)*Math.PI/180,O=Math.sin(c/2)*Math.sin(c/2)+Math.cos(d)*Math.cos(s)*Math.sin(m/2)*Math.sin(m/2);return 6371e3*(2*Math.atan2(Math.sqrt(O),Math.sqrt(1-O)))}routeToTwoNearestCenters(){return v(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){console.log("\u{1F5FA}\uFE0F ALL MAPS: No user location or evacuation centers available");return}try{console.log("\u{1F5FA}\uFE0F ALL MAPS: Finding 2 nearest centers...");let t=this.getTwoNearestCenters(this.userLocation.lat,this.userLocation.lng);if(t.length===0){yield(yield this.toastCtrl.create({message:"No evacuation centers found nearby",duration:3e3,color:"warning"})).present();return}this.clearRoutes(),this.addPulsingMarkers(t),yield this.calculateRoutes(t),yield(yield this.toastCtrl.create({message:`\u{1F5FA}\uFE0F Showing routes to ${t.length} nearest centers via ${this.travelMode}`,duration:3e3,color:"success",position:"top"})).present()}catch(t){console.error("\u{1F5FA}\uFE0F ALL MAPS: Error calculating routes",t),yield(yield this.toastCtrl.create({message:"Error calculating routes. Please try again.",duration:3e3,color:"danger"})).present()}})}getTwoNearestCenters(t,n){return this.evacuationCenters.map(e=>I(E({},e),{distance:this.calculateDistance(t,n,Number(e.latitude),Number(e.longitude))})).sort((e,g)=>e.distance-g.distance).slice(0,2)}addPulsingMarkers(t){t.forEach((n,o)=>{let e=Number(n.latitude),g=Number(n.longitude);if(!isNaN(e)&&!isNaN(g)){let d="assets/Location.png",s="#3880ff";typeof n.disaster_type=="string"&&n.disaster_type.startsWith("Others:")?(d="assets/forOthers.png",s="#9333ea"):n.disaster_type==="Earthquake"?(d="assets/forEarthquake.png",s="#ff9500"):n.disaster_type==="Typhoon"?(d="assets/forTyphoon.png",s="#2dd36f"):n.disaster_type==="Flood"?(d="assets/forFlood.png",s="#3dc2ff"):n.disaster_type==="Fire"?(d="assets/forFire.png",s="#ef4444"):n.disaster_type==="Landslide"?(d="assets/forLandslide.png",s="#8b5a2b"):n.disaster_type==="Others"&&(d="assets/forOthers.png",s="#9333ea");let c=u.divIcon({className:"pulsing-marker",html:`
            <div class="pulse-container">
              <div class="pulse" style="background-color: ${s}"></div>
              <img src="${d}" class="marker-icon" />
              <div class="marker-label">${o+1}</div>
            </div>
          `,iconSize:[50,50],iconAnchor:[25,50]}),m=u.marker([e,g],{icon:c});m.bindPopup(`
          <div class="evacuation-popup nearest-popup">
            <h3>\u{1F3AF} Nearest Center #${o+1}</h3>
            <h4>${n.name}</h4>
            <p><strong>Type:</strong> ${n.disaster_type}</p>
            <p><strong>Distance:</strong> ${(n.distance/1e3).toFixed(2)} km</p>
            <p><strong>Capacity:</strong> ${n.capacity||"N/A"}</p>
          </div>
        `),m.addTo(this.map),this.nearestMarkers.push(m)}})}calculateRoutes(t){return v(this,null,function*(){if(this.userLocation){this.routeLayer=u.layerGroup().addTo(this.map);for(let n=0;n<t.length;n++){let o=t[n],e=Number(o.latitude),g=Number(o.longitude);if(!isNaN(e)&&!isNaN(g))try{let d=this.mapboxRouting.convertTravelModeToProfile(this.travelMode),s=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,g,e,d);if(s&&s.routes&&s.routes.length>0){let c=s.routes[0],m="#3880ff";o.disaster_type==="Earthquake"?m="#ff9500":o.disaster_type==="Typhoon"?m="#2dd36f":o.disaster_type==="Flash Flood"&&(m="#3dc2ff"),u.polyline(c.geometry.coordinates.map(y=>[y[1],y[0]]),{color:m,weight:4,opacity:.8,dashArray:n===0?void 0:"10, 10"}).addTo(this.routeLayer),n===0&&(this.routeTime=c.duration,this.routeDistance=c.distance),console.log(`\u{1F5FA}\uFE0F Route ${n+1}: ${(c.distance/1e3).toFixed(2)}km, ${(c.duration/60).toFixed(0)}min`)}}catch(d){console.error(`\u{1F5FA}\uFE0F Error calculating route to center ${n+1}:`,d)}}}})}clearRoutes(){this.routeLayer&&(this.map.removeLayer(this.routeLayer),this.routeLayer=null),this.nearestMarkers.forEach(t=>{this.map.removeLayer(t)}),this.nearestMarkers=[],this.routeTime=0,this.routeDistance=0}changeTravelMode(t){return v(this,null,function*(){this.travelMode=t,yield(yield this.toastCtrl.create({message:`\u{1F6B6}\u200D\u2642\uFE0F Travel mode changed to ${t}`,duration:2e3,color:"primary"})).present(),this.userLocation&&this.evacuationCenters.length>0&&(yield this.routeToTwoNearestCenters())})}showNavigationPanel(t){return v(this,null,function*(){this.selectedCenter=t,this.selectedTransportMode=null,this.routeInfo={},yield this.calculateAllRoutes(t)})}closeNavigationPanel(){this.selectedCenter=null,this.selectedTransportMode=null,this.routeInfo={}}selectTransportMode(t){return v(this,null,function*(){this.selectedTransportMode=t,this.selectedCenter&&this.routeInfo[t]&&(yield this.routeToCenter(this.selectedCenter,t))})}calculateAllRoutes(t){return v(this,null,function*(){if(!this.userLocation)return;let n=Number(t.latitude),o=Number(t.longitude);if(isNaN(n)||isNaN(o))return;let e=["walking","cycling","driving"];for(let g of e)try{let d=this.mapboxRouting.convertTravelModeToProfile(g),s=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,o,n,d);if(s&&s.routes&&s.routes.length>0){let c=s.routes[0];this.routeInfo[g]={duration:c.duration,distance:c.distance}}}catch(d){console.error(`Error calculating ${g} route:`,d)}})}formatTime(t){return t?`${Math.round(t/60)} min`:"--"}formatDistance(t){if(!t)return"--";let n=t/1e3;return n<1?`${Math.round(t)} m`:`${n.toFixed(1)} km`}startNavigation(){return v(this,null,function*(){if(!this.selectedCenter||!this.selectedTransportMode)return;yield this.routeToCenter(this.selectedCenter,this.selectedTransportMode),this.closeNavigationPanel(),yield(yield this.toastCtrl.create({message:`\u{1F9ED} Navigation started to ${this.selectedCenter.name}`,duration:3e3,color:"success",position:"top"})).present()})}routeToCenter(t,n){return v(this,null,function*(){if(this.userLocation)try{this.clearRoutes();let o=Number(t.latitude),e=Number(t.longitude);if(!isNaN(o)&&!isNaN(e)){let g=this.mapboxRouting.convertTravelModeToProfile(n),d=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,e,o,g);if(d&&d.routes&&d.routes.length>0){let s=d.routes[0],c="#3880ff",m="\u{1F535}";t.disaster_type==="Earthquake"?(c="#ff9500",m="\u{1F7E0}"):t.disaster_type==="Typhoon"?(c="#2dd36f",m="\u{1F7E2}"):t.disaster_type==="Flash Flood"&&(c="#3dc2ff",m="\u{1F535}"),this.routeLayer=u.layerGroup().addTo(this.map);let O=u.polyline(s.geometry.coordinates.map(S=>[S[1],S[0]]),{color:c,weight:5,opacity:.8});O.addTo(this.routeLayer),yield(yield this.toastCtrl.create({message:`${m} Route: ${(s.distance/1e3).toFixed(2)}km, ${(s.duration/60).toFixed(0)}min via ${n}`,duration:4e3,color:"primary"})).present(),this.map.fitBounds(O.getBounds(),{padding:[50,50]})}}}catch(o){console.error("\u{1F5FA}\uFE0F Error routing to center:",o),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}})}goBack(){this.router.navigate(["/tabs/home"])}downloadMap(){return v(this,null,function*(){if(!this.map){yield(yield this.toastCtrl.create({message:"Map not loaded yet. Please wait and try again.",duration:3e3,color:"warning"})).present();return}try{yield this.enhancedDownload.downloadMapWithRoutes("all-maps",this.map,"All-Evacuation-Centers",!0)}catch(t){console.error("Enhanced download error:",t),yield(yield this.toastCtrl.create({message:"Failed to download map. Please try again.",duration:3e3,color:"danger"})).present()}})}ionViewWillLeave(){this.clearRoutes(),this.isRealTimeNavigationActive&&this.osmRouting.stopRealTimeRouting(),this.map&&this.map.remove()}startRealTimeNavigation(t){console.log("\u{1F9ED} Starting real-time navigation to center:",t.name),this.navigationDestination={lat:Number(t.latitude),lng:Number(t.longitude),name:t.name},this.isRealTimeNavigationActive=!0,this.toastCtrl.create({message:`\u{1F9ED} Real-time navigation started to ${t.name}`,duration:3e3,color:"primary"}).then(n=>n.present())}onNavigationRouteUpdated(t){console.log("\u{1F504} All maps navigation route updated"),this.currentNavigationRoute=t,this.updateMapWithNavigationRoute(t)}onNavigationStopped(){console.log("\u23F9\uFE0F All maps real-time navigation stopped"),this.isRealTimeNavigationActive=!1,this.navigationDestination=null,this.currentNavigationRoute=null,this.clearNavigationRoute(),this.toastCtrl.create({message:"\u23F9\uFE0F Navigation stopped",duration:2e3,color:"medium"}).then(t=>t.present())}updateMapWithNavigationRoute(t){if(this.clearNavigationRoute(),t.geometry&&t.geometry.coordinates){let n=this.osmRouting.convertToGeoJSON(t),o=u.geoJSON(n,{style:{color:"#007bff",weight:6,opacity:.8,dashArray:"10, 5"}}).addTo(this.map);o.isNavigationRoute=!0}}clearNavigationRoute(){this.map.eachLayer(t=>{t.isNavigationRoute&&this.map.removeLayer(t)})}static{this.\u0275fac=function(n){return new(n||p)}}static{this.\u0275cmp=R({type:p,selectors:[["app-all-maps"]],standalone:!0,features:[z],decls:78,vars:15,consts:[[3,"fullscreen"],["id","all-maps",2,"height","100%","width","100%"],[1,"map-controls"],["fill","clear",1,"download-btn",3,"click"],["name","download-outline"],[3,"destination","travelMode","autoStart","routeUpdated","navigationStopped",4,"ngIf"],[1,"floating-info"],[1,"info-header"],["name","map","color","secondary"],[1,"disaster-counts"],[1,"count-row"],[1,"disaster-icon"],[1,"disaster-label"],[1,"disaster-count"],[1,"info-text"],["class","route-info",4,"ngIf"],[1,"navigation-panel"],[1,"panel-content"],[1,"panel-header"],["class","center-info",4,"ngIf"],["fill","clear","size","small",3,"click"],["name","close-outline"],["class","transport-options",4,"ngIf"],["vertical","bottom","horizontal","end","slot","fixed"],["color","primary",3,"click"],["name","navigate-outline"],[1,"fab-label"],[3,"routeUpdated","navigationStopped","destination","travelMode","autoStart"],[1,"route-info"],[1,"route-header"],["name","time-outline","color","success"],[1,"route-details"],[1,"route-item"],[3,"name"],["name","location-outline"],[1,"center-info"],[1,"transport-options"],[1,"option-header"],[1,"transport-buttons"],[1,"transport-btn",3,"click"],["name","walk-outline"],["name","bicycle-outline"],["name","car-outline"],["class","start-navigation-btn",3,"click",4,"ngIf"],[1,"time"],[1,"distance"],[1,"start-navigation-btn",3,"click"],["name","navigate"]],template:function(n,o){n&1&&(i(0,"ion-content",0),f(1,"div",1),i(2,"div",2)(3,"ion-button",3),P("click",function(){return o.downloadMap()}),f(4,"ion-icon",4),a()(),x(5,pt,1,3,"app-real-time-navigation",5),i(6,"div",6)(7,"ion-card")(8,"ion-card-content")(9,"div",7),f(10,"ion-icon",8),i(11,"span"),r(12),a()(),i(13,"div",9)(14,"div",10)(15,"span",11),r(16,"\u{1F7E0}"),a(),i(17,"span",12),r(18,"Earthquake:"),a(),i(19,"span",13),r(20),a()(),i(21,"div",10)(22,"span",11),r(23,"\u{1F7E2}"),a(),i(24,"span",12),r(25,"Typhoon:"),a(),i(26,"span",13),r(27),a()(),i(28,"div",10)(29,"span",11),r(30,"\u{1F535}"),a(),i(31,"span",12),r(32,"Flood:"),a(),i(33,"span",13),r(34),a()(),i(35,"div",10)(36,"span",11),r(37,"\u{1F534}"),a(),i(38,"span",12),r(39,"Fire:"),a(),i(40,"span",13),r(41),a()(),i(42,"div",10)(43,"span",11),r(44,"\u{1F7E4}"),a(),i(45,"span",12),r(46,"Landslide:"),a(),i(47,"span",13),r(48),a()(),i(49,"div",10)(50,"span",11),r(51,"\u{1F7E3}"),a(),i(52,"span",12),r(53,"Others:"),a(),i(54,"span",13),r(55),a()(),i(56,"div",10)(57,"span",11),r(58,"\u{1F518}"),a(),i(59,"span",12),r(60,"Multiple:"),a(),i(61,"span",13),r(62),a()()(),i(63,"div",14),r(64," Complete overview of all evacuation centers by disaster type "),a()()()(),x(65,dt,16,3,"div",15),i(66,"div",16)(67,"div",17)(68,"div",18),x(69,gt,5,2,"div",19),i(70,"ion-button",20),P("click",function(){return o.closeNavigationPanel()}),f(71,"ion-icon",21),a()(),x(72,_t,22,10,"div",22),a()(),i(73,"ion-fab",23)(74,"ion-fab-button",24),P("click",function(){return o.routeToTwoNearestCenters()}),f(75,"ion-icon",25),a(),i(76,"ion-label",26),r(77,"Route to 2 Nearest Centers"),a()()()),n&2&&(C("fullscreen",!0),l(5),C("ngIf",o.navigationDestination),l(7),N("All Centers: ",o.centerCounts.total,""),l(8),h(o.centerCounts.earthquake),l(7),h(o.centerCounts.typhoon),l(7),h(o.centerCounts.flood),l(7),h(o.centerCounts.fire),l(7),h(o.centerCounts.landslide),l(7),h(o.centerCounts.others),l(7),h(o.centerCounts.multiple),l(3),C("ngIf",o.routeTime&&o.routeDistance),l(),T("show",o.selectedCenter),l(3),C("ngIf",o.selectedCenter),l(3),C("ngIf",o.selectedCenter))},dependencies:[tt,W,j,B,G,J,H,Y,K,$,F,U,rt],styles:["#all-maps[_ngcontent-%COMP%]{height:100%;width:100%;z-index:1}.map-controls[_ngcontent-%COMP%]{position:absolute;top:95px;left:10px;z-index:1000;display:flex;flex-direction:column;gap:10px}.download-btn[_ngcontent-%COMP%]{--background: rgba(255, 255, 255, .9);--color: #5260ff;--border-radius: 50%;width:44px;height:44px;box-shadow:0 2px 8px #0003}.floating-info[_ngcontent-%COMP%]{position:absolute;top:20px;right:20px;z-index:1000;max-width:280px}.floating-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.floating-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.floating-info[_ngcontent-%COMP%]   .info-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-secondary);margin-bottom:8px}.floating-info[_ngcontent-%COMP%]   .info-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.floating-info[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]{margin:8px 0}.floating-info[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;margin:4px 0;font-size:13px}.floating-info[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%]   .disaster-icon[_ngcontent-%COMP%]{font-size:14px;width:16px;text-align:center}.floating-info[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%]   .disaster-label[_ngcontent-%COMP%]{flex:1;color:var(--ion-color-dark)}.floating-info[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%]   .disaster-count[_ngcontent-%COMP%]{font-weight:600;color:var(--ion-color-secondary);min-width:20px;text-align:right}.floating-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]{font-size:11px;color:var(--ion-color-medium);line-height:1.3;margin-top:8px;padding-top:8px;border-top:1px solid var(--ion-color-light)}ion-toolbar[_ngcontent-%COMP%]{--background: var(--ion-color-secondary);--color: white}ion-title[_ngcontent-%COMP%]{font-weight:600}.transport-controls[_ngcontent-%COMP%]{position:absolute;bottom:120px;left:20px;z-index:1000;max-width:280px}.transport-controls[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.transport-controls[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.transport-controls[_ngcontent-%COMP%]   .transport-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-primary);margin-bottom:8px}.transport-controls[_ngcontent-%COMP%]   .transport-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.transport-controls[_ngcontent-%COMP%]   ion-segment[_ngcontent-%COMP%]{--background: rgba(var(--ion-color-light-rgb), .3);border-radius:8px}.transport-controls[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);--color-checked: var(--ion-color-primary);--indicator-color: var(--ion-color-primary);min-height:40px}.transport-controls[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px;margin-bottom:2px}.transport-controls[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:12px;font-weight:500}.route-info[_ngcontent-%COMP%]{position:absolute;bottom:20px;left:20px;z-index:1000;max-width:200px}.route-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.route-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.route-info[_ngcontent-%COMP%]   .route-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-success);margin-bottom:8px}.route-info[_ngcontent-%COMP%]   .route-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.route-info[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.route-info[_ngcontent-%COMP%]   .route-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:14px;color:var(--ion-color-dark)}.route-info[_ngcontent-%COMP%]   .route-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px;color:var(--ion-color-primary)}.fab-label[_ngcontent-%COMP%]{position:absolute;right:60px;top:50%;transform:translateY(-50%);background:#000000b3;color:#fff;padding:4px 8px;border-radius:4px;font-size:12px;white-space:nowrap;pointer-events:none}[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse-container[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center;justify-content:center}[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse[_ngcontent-%COMP%]{position:absolute;width:60px;height:60px;border-radius:50%;opacity:.6;animation:_ngcontent-%COMP%_pulse 2s infinite;z-index:1}[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-icon[_ngcontent-%COMP%]{width:40px;height:40px;z-index:2;position:relative}[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-label[_ngcontent-%COMP%]{position:absolute;top:-8px;right:-8px;background:var(--ion-color-primary);color:#fff;border-radius:50%;width:20px;height:20px;display:flex;align-items:center;justify-content:center;font-size:12px;font-weight:700;z-index:3;border:2px solid white}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(.8);opacity:.8}50%{transform:scale(1.2);opacity:.4}to{transform:scale(.8);opacity:.8}}.navigation-panel[_ngcontent-%COMP%]{position:fixed;top:0;right:-100%;width:320px;height:100vh;background:#fff;z-index:2000;transition:right .3s ease-in-out;box-shadow:-4px 0 20px #00000026}.navigation-panel.show[_ngcontent-%COMP%]{right:0}.navigation-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;padding:60px 20px 20px}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:24px;padding-bottom:16px;border-bottom:1px solid var(--ion-color-light)}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]{flex:1}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-size:18px;font-weight:600;color:var(--ion-color-dark);line-height:1.2}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--ion-color-medium);line-height:1.3}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);margin:0}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]{flex:1}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .option-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:16px;font-weight:600;color:var(--ion-color-primary)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .option-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-buttons[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;margin-bottom:24px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;border:2px solid var(--ion-color-light);border-radius:12px;background:#fff;cursor:pointer;transition:all .2s ease;position:relative}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]:hover{border-color:var(--ion-color-primary-tint);background:var(--ion-color-primary-tint)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn.active[_ngcontent-%COMP%]{border-color:var(--ion-color-primary);background:var(--ion-color-primary-tint)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:var(--ion-color-primary)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;margin-right:12px;color:var(--ion-color-medium);transition:color .2s ease}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:var(--ion-color-dark);flex:1}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-end;gap:2px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:var(--ion-color-primary)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]{width:100%;padding:16px;background:var(--ion-color-primary);color:#fff;border:none;border-radius:12px;font-size:16px;font-weight:600;display:flex;align-items:center;justify-content:center;gap:8px;cursor:pointer;transition:background .2s ease}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]:hover{background:var(--ion-color-primary-shade)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]{text-align:center;min-width:200px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:var(--ion-color-secondary);font-size:16px;font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:4px 0;color:var(--ion-color-dark);font-size:14px;font-weight:500}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0;font-size:14px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:var(--ion-color-dark)}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup.nearest-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--ion-color-success);font-size:18px}"]})}}return p})();export{Dt as AllMapsPage};
